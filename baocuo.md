> Task :app:compileDebugKotlin
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:47:28 Classifier 'Loading' does not have a companion object, and thus must be initialized here
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:326:28 Classifier 'Loading' does not have a companion object, and thus must be initialized here
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/UserRepository.kt:448:28 Classifier 'Loading' does not have a companion object, and thus must be initialized here
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:533:50 Unresolved reference: body
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/intelligence/IntelligenceViewModel.kt:68:44 Cannot access 'songDao': it is private in 'MusicDataSource'
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/intelligence/IntelligenceViewModel.kt:77:38 Unresolved reference: isVip
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/intelligence/IntelligenceViewModel.kt:79:36 Unresolved reference: url
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/intelligence/IntelligenceViewModel.kt:99:40 Unresolved reference: isVip
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/intelligence/IntelligenceViewModel.kt:101:38 Unresolved reference: url
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:206:52 Unresolved reference: showPhoneLoginDialog
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/LyricAdapter.kt:53:13 Val cannot be reassigned
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:184:17 Unresolved reference: buttonPlayerShare
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:185:34 Unresolved reference: it
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:381:67 Unresolved reference: position
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:382:88 Unresolved reference: position
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:383:86 Unresolved reference: duration
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:384:62 Unresolved reference: duration
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:387:46 Unresolved reference: position
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:525:58 Unresolved reference: Drawable
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:529:57 Unresolved reference: Drawable
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1014:69 Unresolved reference: button_playlist_close
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtProcessor.kt:113:31 Object is not abstract and does not implement abstract member public abstract fun onResourceReady(p0: Bitmap, p1: Any, p2: Target<Bitmap!>!, p3: DataSource, p4: Boolean): Boolean defined in com.bumptech.glide.request.RequestListener
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtProcessor.kt:125:25 'onResourceReady' overrides nothing
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtProcessor.kt:140:29 Suspension functions can be called only within coroutine body
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:521:62 Object is not abstract and does not implement abstract member public abstract fun onResourceReady(p0: Bitmap, p1: Any, p2: Target<Bitmap!>!, p3: DataSource, p4: Boolean): Boolean defined in com.bumptech.glide.request.RequestListener
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:532:21 'onResourceReady' overrides nothing
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:544:64 Object is not abstract and does not implement abstract member public abstract fun onResourceReady(p0: Drawable, p1: Any, p2: Target<Drawable!>!, p3: DataSource, p4: Boolean): Boolean defined in com.bumptech.glide.request.RequestListener
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:555:21 'onResourceReady' overrides nothing
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/LyricCache.kt:80:13 'if' must have both main and 'else' branches if used as an expression
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:481:46 Type mismatch: inferred type is List<CommentDto> but List<Comment> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:486:43 Type mismatch: inferred type is List<CommentDto> but List<Comment> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:488:43 Type mismatch: inferred type is CommentDto but Comment was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:488:43 Type mismatch: inferred type is List<CommentDto> but Iterable<Comment> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:488:43 Type mismatch: inferred type is Iterable<CommentDto> but Iterable<Comment> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:488:66 Type mismatch: inferred type is List<CommentDto>? but Iterable<Comment>? was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:518:39 Type mismatch: inferred type is CommentDto but Comment was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:518:39 Type mismatch: inferred type is List<CommentDto> but Iterable<Comment> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:518:39 Type mismatch: inferred type is Iterable<CommentDto> but Iterable<Comment> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:518:62 Type mismatch: inferred type is List<CommentDto>? but Iterable<Comment>? was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:10:45 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:51:68 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:52:51 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:53:46 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:56:58 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:57:41 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:58:36 Unresolved reference: Playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:100:37 Unresolved reference: getBanners
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:104:24 Incompatible types: NetworkResult.Success<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:104:24 One type argument expected. Use 'Success<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:105:49 Type mismatch: inferred type is Any? but List<Banner> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:107:24 Incompatible types: NetworkResult.Error<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:107:24 One type argument expected. Use 'Error<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:110:24 Incompatible types: NetworkResult.Loading<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:110:24 One type argument expected. Use 'Loading<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:134:37 Unresolved reference: getRecommendPlaylists
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:138:24 Incompatible types: NetworkResult.Success<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:138:24 One type argument expected. Use 'Success<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:139:60 Type mismatch: inferred type is Any? but List<[Error type: Unresolved type for Playlist]> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:141:24 Incompatible types: NetworkResult.Error<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:141:24 One type argument expected. Use 'Error<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:144:24 Incompatible types: NetworkResult.Loading<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:144:24 One type argument expected. Use 'Loading<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:168:37 Unresolved reference: getToplists
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:172:24 Incompatible types: NetworkResult.Success<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:172:24 One type argument expected. Use 'Success<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:173:50 Type mismatch: inferred type is Any? but List<[Error type: Unresolved type for Playlist]> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:175:24 Incompatible types: NetworkResult.Error<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:175:24 One type argument expected. Use 'Error<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:178:24 Incompatible types: NetworkResult.Loading<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:178:24 One type argument expected. Use 'Loading<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:236:37 Unresolved reference: getNewAlbums
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:240:24 Incompatible types: NetworkResult.Success<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:240:24 One type argument expected. Use 'Success<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:241:51 Type mismatch: inferred type is Any? but List<Album> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:243:24 Incompatible types: NetworkResult.Error<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:243:24 One type argument expected. Use 'Error<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:246:24 Incompatible types: NetworkResult.Loading<*> and Unit
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:246:24 One type argument expected. Use 'Loading<*>' if you don't want to pass type arguments
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:12:34 Unresolved reference: util
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:154:37 Unresolved reference: getRecommendedSongs
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:158:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:159:65 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:161:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:162:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:162:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:164:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:10:34 Unresolved reference: util
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:68:17 'when' expression must be exhaustive, add necessary 'is Error', 'is Loading', 'is Success' branches or 'else' branch instead
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:69:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:70:47 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:72:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:73:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:75:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:107:13 No value passed for parameter 'call'
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:107:21 Type mismatch: inferred type is () -> List<Song> but Long was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:107:39 Suspension functions can be called only within coroutine body
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:107:71 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:109:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:112:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:115:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:129:17 'when' expression must be exhaustive, add necessary 'is Error', 'is Loading', 'is Success' branches or 'else' branch instead
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:130:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:133:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:135:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:137:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModelExt.kt:139:35 Cannot access 'handleError': it is protected in 'FlowViewModel'
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModelExt.kt:141:39 None of the following functions can be called with the arguments supplied: 
public constructor ApiCallback<T : Any!>(responseLiveData: (MutableLiveData<(ApiResponse<(T#1 (type parameter of com.example.aimusicplayer.viewmodel.executeApiCallWithCallback)..T#1?)>..ApiResponse<T#1!>?)>..MutableLiveData<ApiResponse<T#1!>!>?), errorMessageLiveData: MutableLiveData<String!>!) defined in com.example.aimusicplayer.api.ApiCallback
public constructor ApiCallback<T : Any!>(responseLiveData: MutableLiveData<ApiResponse<T#1!>!>!, errorMessageLiveData: MutableLiveData<String!>!, loadingLiveData: MutableLiveData<Boolean!>!, showLoading: Boolean) defined in com.example.aimusicplayer.api.ApiCallback
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModelExt.kt:209:39 Cannot access 'setLoading': it is protected in 'FlowViewModel'
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModelExt.kt:210:44 Cannot access 'setLoading': it is protected in 'FlowViewModel'
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModelExt.kt:222:48 Cannot access 'handleError': it is protected in 'FlowViewModel'
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:20:34 Unresolved reference: util
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:293:17 'when' expression must be exhaustive, add necessary 'is Error', 'is Loading', 'is Success' branches or 'else' branch instead
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:294:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:295:56 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:297:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:298:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:298:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:301:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:11:34 Unresolved reference: util
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:101:37 Unresolved reference: getLocalSongs
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:105:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:106:60 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:108:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:109:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:109:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:111:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:135:37 Unresolved reference: getLikedSongs
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:139:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:140:62 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:142:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:143:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:143:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:145:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:169:37 Unresolved reference: getUserPlaylists
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:173:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:174:60 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:176:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:177:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:177:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:179:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:203:37 Unresolved reference: getCollectedPlaylists
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:207:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:208:66 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:210:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:211:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:211:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:213:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:279:37 Unresolved reference: searchSongs
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:283:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:284:58 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:286:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:287:54 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:287:71 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MusicLibraryViewModel.kt:289:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:354:56 Unresolved reference: lrc
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:355:64 Unresolved reference: tlyric
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:372:54 Type mismatch: inferred type is String but LyricInfo was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:381:70 Type mismatch: inferred type is LyricInfo but String was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:388:40 Type mismatch: inferred type is LyricInfo? but String was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:604:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:604:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:622:35 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:624:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:624:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:642:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:642:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:660:35 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:662:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:662:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:676:31 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:682:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:682:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:703:39 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:707:35 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:709:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:709:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:722:31 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:728:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:728:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:736:39 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:738:39 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:742:35 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:744:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:744:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:757:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:757:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:764:35 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:766:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:766:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:784:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:784:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:799:39 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:801:39 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:805:35 Unresolved reference: value
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:807:17 Unresolved reference: _isLoadingFlow
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:807:32 Variable expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:834:26 Unresolved reference: playAtIndex
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:13:34 Unresolved reference: util
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:50:9 'errorMessage' hides member of supertype 'FlowViewModel' and needs 'override' modifier
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:102:13 Cannot find a parameter with this name: context
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:107:17 Suspension functions can be called only within coroutine body
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:133:17 'when' expression must be exhaustive, add necessary 'is Error', 'is Loading', 'is Success' branches or 'else' branch instead
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:134:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:135:55 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:136:62 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:137:44 Unresolved reference: data
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:150:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:151:53 Unresolved reference: message
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:153:37 Overload resolution ambiguity: 
public final fun `<init>`(p0: String!): kotlin.Exception /* = java.lang.Exception */ defined in kotlin.Exception
public final fun `<init>`(p0: Throwable!): kotlin.Exception /* = java.lang.Exception */ defined in kotlin.Exception
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:156:24 Unresolved reference: NetworkResult
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:169:9 Unresolved reference: clearError

> Task :app:compileDebugKotlin FAILED

