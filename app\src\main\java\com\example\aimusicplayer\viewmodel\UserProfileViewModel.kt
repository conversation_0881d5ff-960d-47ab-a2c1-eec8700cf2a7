package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.UserDetailResponse
import com.example.aimusicplayer.data.model.UserSubCountResponse
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.data.model.User
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 用户资料的ViewModel
 * 负责处理用户资料的业务逻辑
 * 使用Kotlin协程处理异步操作
 * 使用Flow管理UI状态
 */
@HiltViewModel
class UserProfileViewModel @Inject constructor(
    application: Application,
    private val userRepository: UserRepository
) : FlowViewModel(application) {

    companion object {
        private const val TAG = "UserProfileViewModel"
        private const val MAX_RETRY_COUNT = 3
    }

    // 用户信息的StateFlow
    private val _userDataFlow = MutableStateFlow<User?>(null)
    val userDataFlow: StateFlow<User?> = _userDataFlow.asStateFlow()
    val userData: LiveData<User?> = userDataFlow.asLiveData() // 兼容LiveData

    // 加载状态已经在FlowViewModel中定义，这里直接使用
    val isLoading: LiveData<Boolean> = loadingFlow.asLiveData() // 兼容LiveData

    // 错误信息已经在FlowViewModel中定义，这里直接使用
    override val errorMessage: LiveData<String> = errorMessageFlow.asLiveData() // 兼容LiveData

    // 重试次数
    private var retryCount = 0

    init {
        // 加载用户数据
        loadUserData()
    }

    /**
     * 加载用户数据
     */
    fun loadUserData() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载用户数据失败", e)
                handleError(e, "加载用户数据失败: ${e.message}")
                setLoading(false)
            }
        ) {
            setLoading(true)

            // 从UserRepository获取用户数据
            val isLoggedIn = userRepository.isLoggedIn()
            if (isLoggedIn) {
                val userId = userRepository.getUserId()
                val username = userRepository.getUsername()
                val token = userRepository.getUserToken()
                val avatarUrl = userRepository.getAvatarUrl()

                val user = User(userId, username, token, avatarUrl)
                _userDataFlow.value = user

                // 获取更详细的用户信息
                fetchUserDetail(userId)

                Log.d(TAG, "加载用户数据成功: $username")
            } else {
                Log.d(TAG, "用户未登录")
                _userDataFlow.value = null
                setLoading(false)
            }
        }
    }

    /**
     * 获取用户详情
     * @param userId 用户ID
     */
    private suspend fun fetchUserDetail(userId: String) {
        launchSafely(onError = { e ->
            Log.e(TAG, "获取用户详情失败", e)
            // 尝试重试
            if (retryCount < MAX_RETRY_COUNT) {
                retryCount++
                Log.d(TAG, "正在重试获取用户详情，第 $retryCount 次")
                // 延迟1秒后重试
                viewModelScope.launch {
                    delay(1000)
                    fetchUserDetail(userId)
                }
            } else {
                // 重试次数用完，显示错误
                retryCount = 0
                handleError(e, "获取用户详情失败: ${e.message}")
                setLoading(false)
            }
        }) {
            // 在IO线程获取用户详情和统计信息
            val detailResult = withContext(Dispatchers.IO) {
                userRepository.getUserDetail(userId)
            }
            val subCountResult = withContext(Dispatchers.IO) {
                userRepository.getUserSubCount()
            }

            // 在主线程更新UI
            when (detailResult) {
                is NetworkResult.Success -> {
                    val userDetail = detailResult.data
                    val subCount = if (subCountResult is NetworkResult.Success) {
                        subCountResult.data
                    } else {
                        null
                    }

                    // 更新用户信息
                    val updatedUser = userRepository.updateUserInfo(userDetail, subCount)
                    updatedUser?.let {
                        _userDataFlow.value = it
                        retryCount = 0 // 成功后重置重试次数
                    }
                    setLoading(false)
                }
                is NetworkResult.Error -> {
                    val errorMsg = detailResult.message
                    Log.e(TAG, "获取用户详情失败: $errorMsg")
                    // 错误已在onError回调中处理，此处不再重复处理 setLoading(false)
                }
                is NetworkResult.Loading -> {
                    // 加载中状态处理，通常由 setLoading(true) 在 launchSafely 开始时处理
                }
                // 添加 else 分支以确保 when 语句是详尽的
                else -> {
                    Log.w(TAG, "Unhandled NetworkResult state: $detailResult")
                    setLoading(false)
                }
            }
        }
    }

    /**
     * 重试加载用户数据
     */
    fun retryLoadUserData() {
        retryCount = 0
        // clearError() // Removed: clearError() is not defined in FlowViewModel or its parent
        loadUserData()
    }

    /**
     * 退出登录 (挂起函数版本)
     */
    suspend fun logout() {
        try {
            setLoading(true)

            // 调用UserRepository清除登录状态
            withContext(Dispatchers.IO) {
                userRepository.logout()
            }

            _userDataFlow.value = null
            setLoading(false)

            Log.d(TAG, "退出登录成功")
        } catch (e: Exception) {
            Log.e(TAG, "退出登录失败", e)
            handleError(e, "退出登录失败: ${e.message}")
            setLoading(false)
            throw e
        }
    }

    /**
     * 退出登录 (非挂起函数版本，兼容Java调用)
     */
    fun logoutSync() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "启动退出登录协程失败", e)
                handleError(e, "退出登录失败: ${e.message}")
                setLoading(false)
            }
        ) {
            setLoading(true)

            // 调用UserRepository清除登录状态
            withContext(Dispatchers.IO) {
                try {
                    userRepository.logout()

                    withContext(Dispatchers.Main) {
                        _userDataFlow.value = null
                        setLoading(false)
                        Log.d(TAG, "退出登录成功")
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        Log.e(TAG, "退出登录失败", e)
                        handleError(e, "退出登录失败: ${e.message}")
                        setLoading(false)
                    }
                }
            }
        }
    }
}
